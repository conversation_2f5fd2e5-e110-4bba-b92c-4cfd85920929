/**
 * Frida Ruin Seller - Standalone automated ruin selling script for Dominations
 *
 * This script automatically discovers and processes GoodyHut instances with sellable ruins,
 * calling the SellRuins() method (Token 0x6002B93, Address RVA "0x209DE3C") to clear completed instances.
 *
 * Usage: frida -U -l frida-ruin-seller.js com.nexonm.dominations.adk
 */
import "frida-il2cpp-bridge";

// Frida script initialization
console.log("🗑️ Frida Ruin Seller v1.0 - Starting initialization...");

// Frida type declarations - using frida-il2cpp-bridge
declare const Java: any;

// Type definitions for Il2Cpp interop
interface Il2CppObject {
    handle: NativePointer;
    class: Il2CppClass;
    isNull(): boolean;
    method(name: string): Il2CppMethod | null;
    field(name: string): Il2CppField | null;
}

interface Il2CppClass {
    name: string;
    handle: NativePointer;
    methods: Il2CppMethod[];
    fields: Il2CppField[];
}

interface Il2CppMethod {
    name: string;
    handle: NativePointer;
    invoke(...args: any[]): any;
}

interface Il2CppField {
    name: string;
    type: string;
    value: any;
}

interface ProcessingStats {
    totalInstances: number;
    validGoodyHuts: number;
    completedInstances: number;
    ruinSellAttempts: number;
    successfulSells: number;
    failedSells: number;
    accessViolations: number;
    methodNotFound: number;
    connectionErrors: number;
    retryAttempts: number;
    startTime: number;
    endTime?: number;
    executionTimeMs?: number;
}

interface InstanceValidation {
    isValid: boolean;
    hasGoodyHut: boolean;
    isCompleted: boolean;
    hasRuins: boolean;
    canSell: boolean;
    state: string;
    rewardType: string;
    rewardAmount: number | null;
    error?: string;
}

class FridaRuinSeller {
    private stats: ProcessingStats;
    private assemblyImage: any;
    private entityControllerClass: any;
    private isConnectionHealthy: boolean = true;
    private lastConnectionCheck: number = 0;

    constructor() {
        this.stats = {
            totalInstances: 0,
            validGoodyHuts: 0,
            completedInstances: 0,
            ruinSellAttempts: 0,
            successfulSells: 0,
            failedSells: 0,
            accessViolations: 0,
            methodNotFound: 0,
            connectionErrors: 0,
            retryAttempts: 0,
            startTime: Date.now()
        };
    }

    /**
     * Check connection health and attempt recovery if needed
     */
    private checkConnectionHealth(): boolean {
        const now = Date.now();

        // Only check every 5 seconds to avoid overhead
        if (now - this.lastConnectionCheck < 5000) {
            return this.isConnectionHealthy;
        }

        this.lastConnectionCheck = now;

        try {
            // Try to access Il2Cpp domain to verify connection
            if (!Il2Cpp.domain || !this.assemblyImage) {
                this.isConnectionHealthy = false;
                console.log("⚠️ Connection health check failed - Il2Cpp domain or assembly not accessible");
                return false;
            }

            // Try to access the EntityController class instead of Object (which may not exist in Assembly-CSharp)
            if (!this.entityControllerClass) {
                this.isConnectionHealthy = false;
                console.log("⚠️ Connection health check failed - EntityController class not accessible");
                return false;
            }

            // Try to get instances to verify the connection is working
            const testInstances = Il2Cpp.gc.choose(this.entityControllerClass);
            if (!testInstances) {
                this.isConnectionHealthy = false;
                console.log("⚠️ Connection health check failed - Cannot access entity instances");
                return false;
            }

            this.isConnectionHealthy = true;
            return true;

        } catch (error) {
            this.isConnectionHealthy = false;
            this.stats.connectionErrors++;
            console.log(`⚠️ Connection health check failed: ${error}`);
            return false;
        }
    }

    /**
     * Attempt to recover connection and reinitialize
     */
    private async attemptConnectionRecovery(): Promise<boolean> {
        console.log("🔄 Attempting connection recovery...");

        try {
            // Wait a bit before attempting recovery
            await new Promise(resolve => setTimeout(resolve, 2000));

            // Try to reinitialize
            const recovered = await this.initialize();
            if (recovered) {
                this.isConnectionHealthy = true;
                console.log("✅ Connection recovery successful");
                return true;
            } else {
                console.log("❌ Connection recovery failed");
                return false;
            }

        } catch (error) {
            console.log(`❌ Connection recovery error: ${error}`);
            return false;
        }
    }

    /**
     * Discover available classes in the Assembly-CSharp image
     */
    private discoverClasses(): string[] {
        try {
            console.log("🔍 Discovering available classes...");

            const classes: string[] = [];
            const image = this.assemblyImage;

            // Try to enumerate classes (this depends on frida-il2cpp-bridge implementation)
            if (image && image.classes) {
                for (const cls of image.classes) {
                    if (cls.name) {
                        classes.push(cls.name);
                    }
                }
            }

            // Filter for potential entity/controller classes
            const entityClasses = classes.filter(name =>
                name.toLowerCase().includes('entity') ||
                name.toLowerCase().includes('controller') ||
                name.toLowerCase().includes('goody') ||
                name.toLowerCase().includes('building') ||
                name.toLowerCase().includes('structure')
            );

            console.log(`📋 Found ${classes.length} total classes, ${entityClasses.length} potential entity classes`);

            if (entityClasses.length > 0) {
                console.log("🎯 Potential entity classes:");
                entityClasses.slice(0, 10).forEach(name => console.log(`   - ${name}`));
                if (entityClasses.length > 10) {
                    console.log(`   ... and ${entityClasses.length - 10} more`);
                }
            }

            return entityClasses;

        } catch (error) {
            console.log(`❌ Class discovery failed: ${error}`);
            return [];
        }
    }

    /**
     * Wait for the game to fully load and entities to be available
     */
    private async waitForGameToLoad(): Promise<boolean> {
        console.log("⏳ Waiting for game to fully load...");

        const maxWaitTime = 120000; // 2 minutes maximum wait
        const checkInterval = 5000; // Check every 5 seconds
        const startTime = Date.now();

        while (Date.now() - startTime < maxWaitTime) {
            try {
                if (this.entityControllerClass) {
                    const instances = Il2Cpp.gc.choose(this.entityControllerClass);
                    console.log(`🔍 Checking entity count: ${instances.length} EntityController instances found`);

                    if (instances.length > 100) { // Wait for a reasonable number of entities
                        console.log(`✅ Game appears to be loaded with ${instances.length} entities`);
                        return true;
                    }
                }

                const elapsed = ((Date.now() - startTime) / 1000).toFixed(1);
                console.log(`⏳ Still waiting for game to load... (${elapsed}s elapsed)`);
                await new Promise(resolve => setTimeout(resolve, checkInterval));

            } catch (error) {
                console.log(`⚠️ Error while waiting for game load: ${error}`);
                await new Promise(resolve => setTimeout(resolve, checkInterval));
            }
        }

        console.log("❌ Timeout waiting for game to load");
        return false;
    }

    /**
     * Initialize Il2Cpp domain and get required classes with discovery
     */
    private async initialize(): Promise<boolean> {
        try {
            console.log("🔧 Initializing Il2Cpp domain...");

            // Get Assembly-CSharp image using frida-il2cpp-bridge
            this.assemblyImage = Il2Cpp.domain.assembly("Assembly-CSharp").image;
            if (!this.assemblyImage) {
                console.log("❌ Failed to get Assembly-CSharp image");
                return false;
            }

            // Discover available classes first for debugging
            this.discoverClasses();

            // Try multiple potential class names for entity controllers
            const potentialClassNames = [
                "EntityController",
                "Entity",
                "BaseEntity",
                "GameEntity",
                "BuildingController",
                "StructureController",
                "GoodyHutController",
                "CollectibleController"
            ];

            // First, find the EntityController class (even if it has no instances yet)
            for (const className of potentialClassNames) {
                try {
                    this.entityControllerClass = this.assemblyImage.class(className);
                    if (this.entityControllerClass) {
                        console.log(`✅ Found entity class: ${className}`);

                        // Don't require instances yet - just find the class
                        const testInstances = Il2Cpp.gc.choose(this.entityControllerClass);
                        console.log(`🔍 ${className} has ${testInstances.length} instances`);

                        // If we found the class, break and wait for game to load
                        if (className === "EntityController") {
                            break;
                        }
                    }
                } catch (error) {
                    console.log(`⚠️ Failed to get class ${className}: ${error}`);
                }
            }

            if (!this.entityControllerClass) {
                console.log("❌ Could not find EntityController class");
                return false;
            }

            // Now wait for the game to fully load and populate entities
            const gameLoaded = await this.waitForGameToLoad();
            if (!gameLoaded) {
                console.log("❌ Game did not load within timeout period");
                return false;
            }

            console.log("✅ Initialization completed - game is loaded and ready");
            return true;

        } catch (error) {
            console.log(`❌ Initialization failed: ${error}`);
            return false;
        }
    }

    /**
     * Validate TagEnum property to identify sellable ruin entities
     */
    private validateTagEnum(instance: any, index: number): { isRuin: boolean, tagEnum: string } {
        try {
            console.log(`🔍 [${index}] Checking TagEnum to identify ruin type...`);

            // First, try to get TagEnum directly from the EntityController instance
            let tagEnumValue: string = "UNKNOWN";

            // Try multiple approaches to access TagEnum
            const tagEnumAccessMethods = [
                // Direct access on EntityController
                () => instance.field("TagEnum")?.value,
                () => instance.field("m_tagEnum")?.value,
                () => instance.field("_tagEnum")?.value,

                // Access through GoodyHut component
                () => {
                    const goodyHutFieldNames = ["m_goodyHut", "goodyHut", "_goodyHut", "m_GoodyHut"];
                    for (const fieldName of goodyHutFieldNames) {
                        try {
                            const goodyHutField = instance.field(fieldName);
                            if (goodyHutField && goodyHutField.value && this.isValidInstance(goodyHutField.value)) {
                                const goodyHutInstance = goodyHutField.value;

                                // Try to get TagEnum from GoodyHut instance
                                const tagEnum = goodyHutInstance.field("TagEnum")?.value ||
                                              goodyHutInstance.field("m_tagEnum")?.value ||
                                              goodyHutInstance.field("_tagEnum")?.value;
                                if (tagEnum) return tagEnum;
                            }
                        } catch (error) {
                            continue;
                        }
                    }
                    return null;
                },

                // Access through Entity or GameObject if available
                () => {
                    const entityFieldNames = ["m_entity", "entity", "_entity", "m_gameObject", "gameObject"];
                    for (const fieldName of entityFieldNames) {
                        try {
                            const entityField = instance.field(fieldName);
                            if (entityField && entityField.value && this.isValidInstance(entityField.value)) {
                                const entityInstance = entityField.value;
                                const tagEnum = entityInstance.field("TagEnum")?.value ||
                                              entityInstance.field("m_tagEnum")?.value ||
                                              entityInstance.field("_tagEnum")?.value;
                                if (tagEnum) return tagEnum;
                            }
                        } catch (error) {
                            continue;
                        }
                    }
                    return null;
                }
            ];

            // Try each method to get TagEnum
            for (const method of tagEnumAccessMethods) {
                try {
                    const result = method();
                    if (result !== null && result !== undefined) {
                        tagEnumValue = result.toString();
                        console.log(`🏷️ [${index}] Found TagEnum: ${tagEnumValue}`);
                        break;
                    }
                } catch (error) {
                    // Continue to next method
                    continue;
                }
            }

            // Check if the TagEnum indicates this is a sellable ruin
            const isRuin = this.isRuinTagEnum(tagEnumValue);

            console.log(`🔍 [${index}] TagEnum validation result: ${tagEnumValue} -> ${isRuin ? 'IS RUIN' : 'NOT RUIN'}`);

            return {
                isRuin: isRuin,
                tagEnum: tagEnumValue
            };

        } catch (error) {
            console.log(`❌ [${index}] TagEnum validation error: ${error}`);
            return {
                isRuin: false,
                tagEnum: "ERROR"
            };
        }
    }

    /**
     * Check if a TagEnum value indicates a sellable ruin entity
     */
    private isRuinTagEnum(tagEnum: string): boolean {
        if (!tagEnum || tagEnum === "UNKNOWN" || tagEnum === "ERROR") {
            return false;
        }

        const ruinTags = [
            "GoodyRuins",
            "GoodyRuins6",
            "GOODY_RUINS",
            "GOODY_RUINS_6",
            "Ruins",
            "Ruin"
        ];

        const tagEnumUpper = tagEnum.toUpperCase();

        // Check for exact matches or partial matches
        for (const ruinTag of ruinTags) {
            if (tagEnumUpper === ruinTag.toUpperCase() ||
                tagEnumUpper.includes(ruinTag.toUpperCase()) ||
                tagEnumUpper.includes("RUIN")) {
                return true;
            }
        }

        return false;
    }

    /**
     * Enhanced instance validation with comprehensive memory safety checks
     */
    private isValidInstance(instance: any): boolean {
        try {
            // Null check
            if (!instance) {
                return false;
            }

            // Handle validation with multiple safety checks
            if (!instance.handle) {
                return false;
            }

            // Check for null handle using multiple methods
            if (instance.handle.isNull && instance.handle.isNull()) {
                return false;
            }

            // Additional null pointer checks
            const handleAddr = instance.handle.toString();
            if (handleAddr === "0x0" || handleAddr === "null" || handleAddr === "(nil)") {
                return false;
            }

            // Validate handle is not pointing to invalid memory regions
            const handleValue = instance.handle.toInt32();
            if (handleValue === 0 || handleValue < 0x1000) { // Below typical valid memory range
                return false;
            }

            // Try to access the instance's class with error handling
            try {
                if (!instance.class) {
                    return false;
                }

                // Validate class name is accessible
                if (!instance.class.name || instance.class.name === "") {
                    return false;
                }
            } catch (classError) {
                return false;
            }

            // Memory validation with multiple safety checks
            try {
                // Try to read from the handle to verify memory is accessible
                const testByte = instance.handle.readU8();

                // Additional validation: try reading from a few bytes ahead
                instance.handle.add(4).readU8();

                return true;
            } catch (memError) {
                // Memory is not accessible
                return false;
            }
        } catch (error) {
            // Any unexpected error means the instance is not valid
            return false;
        }
    }

    /**
     * Safe method invocation with enhanced validation and error handling
     */
    private safeInvoke(instance: any, methodName: string, ...args: any[]): { error: string | null, value: any } {
        try {
            // Enhanced instance validation
            if (!this.isValidInstance(instance)) {
                this.stats.accessViolations++;
                return { error: "Invalid or inaccessible instance", value: null };
            }

            // Try to get the method
            const method = instance.method(methodName);
            if (!method) {
                this.stats.methodNotFound++;
                return { error: `Method ${methodName} not found`, value: null };
            }
            
            let result: any;

            try {
                // First try with provided arguments
                if (args.length > 0) {
                    result = (method as any).invoke(...args);
                } else {
                    result = method.invoke();
                }
            } catch (paramError: any) {
                const paramErrorMsg = String(paramError);

                // Handle connection errors
                if (paramErrorMsg.includes("device") || paramErrorMsg.includes("connection") ||
                    paramErrorMsg.includes("lost") || paramErrorMsg.includes("disconnected")) {
                    this.stats.connectionErrors++;
                    return { error: `Connection error: ${paramErrorMsg}`, value: null };
                }

                // Handle access violations more specifically
                if (paramErrorMsg.includes("access violation") || paramErrorMsg.includes("0x0") ||
                    paramErrorMsg.includes("invalid memory") || paramErrorMsg.includes("segmentation")) {
                    this.stats.accessViolations++;
                    return { error: `Access violation: ${paramErrorMsg}`, value: null };
                }

                // Handle "bad argument count" errors with parameter discovery
                if (paramErrorMsg.includes("bad argument count")) {
                    // Try common parameter patterns
                    const parameterAttempts = [
                        [], // No parameters
                        [true], // Boolean parameter
                        [false], // Boolean parameter (opposite)
                        [0], // Integer parameter
                        [1], // Integer parameter
                        [null], // Null parameter
                        [true, 0], // Boolean + integer
                        [false, 0], // Boolean + integer
                    ];

                    for (const params of parameterAttempts) {
                        try {
                            result = (method as any).invoke(...params);
                            // Success - log working parameters for debugging
                            if (params.length > 0) {
                                console.log(`✅ ${methodName} succeeded with parameters: [${params.join(', ')}]`);
                            }
                            break;
                        } catch (attemptError) {
                            continue; // Try next parameter combination
                        }
                    }

                    // If all attempts failed, return method-specific safe defaults
                    if (result === undefined) {
                        if (methodName === "CanCollect" || methodName.includes("Can")) {
                            return { error: null, value: false };
                        } else if (methodName === "IsJobComplete" || methodName.includes("Is")) {
                            return { error: null, value: false };
                        } else if (methodName.includes("Get") && methodName.includes("Amount")) {
                            return { error: null, value: 0 };
                        } else if (methodName.includes("Get") && methodName.includes("Type")) {
                            return { error: null, value: "UNKNOWN" };
                        } else {
                            return { error: `Parameter error: ${paramErrorMsg}`, value: null };
                        }
                    }
                } else {
                    throw paramError; // Re-throw non-parameter errors
                }
            }
            
            return { error: null, value: result };
            
        } catch (error) {
            const errorMsg = String(error);
            if (errorMsg.includes("access violation") || errorMsg.includes("0x0")) {
                return { error: "Access violation - invalid instance", value: null };
            }
            return { error: `Method error: ${errorMsg}`, value: null };
        }
    }

    /**
     * Validate if an EntityController instance has a GoodyHut with sellable ruins
     */
    private validateInstance(instance: any, index: number): InstanceValidation {
        const validation: InstanceValidation = {
            isValid: false,
            hasGoodyHut: false,
            isCompleted: false,
            hasRuins: false,
            canSell: false,
            state: "UNKNOWN",
            rewardType: "UNKNOWN",
            rewardAmount: null
        };

        try {
            // First, validate the instance itself
            if (!this.isValidInstance(instance)) {
                validation.error = `Invalid instance at index ${index}`;
                return validation;
            }

            // Check TagEnum first to identify if this is actually a ruin entity
            const tagEnumValidation = this.validateTagEnum(instance, index);
            if (!tagEnumValidation.isRuin) {
                validation.error = `Not a ruin entity - TagEnum: ${tagEnumValidation.tagEnum}`;
                return validation;
            }

            console.log(`🏷️ [${index}] Confirmed ruin entity - TagEnum: ${tagEnumValidation.tagEnum}`);
            validation.rewardType = tagEnumValidation.tagEnum; // Store the actual tag for reference

            // Try multiple field names for GoodyHut component
            const goodyHutFieldNames = ["m_goodyHut", "goodyHut", "_goodyHut", "m_GoodyHut"];
            let goodyHutInstance: any = null;

            for (const fieldName of goodyHutFieldNames) {
                try {
                    const goodyHutField = instance.field(fieldName);
                    if (goodyHutField && goodyHutField.value && goodyHutField.value.toString() !== "0x0") {
                        goodyHutInstance = goodyHutField.value;
                        validation.hasGoodyHut = true;
                        break;
                    }
                } catch (fieldError) {
                    // Continue trying other field names
                    continue;
                }
            }

            if (!goodyHutInstance) {
                validation.error = "No accessible GoodyHut component found";
                return validation;
            }

            // Validate the GoodyHut instance itself
            if (!this.isValidInstance(goodyHutInstance)) {
                validation.error = "GoodyHut instance is invalid";
                return validation;
            }

            // Check if job is complete
            const isCompleteResult = this.safeInvoke(goodyHutInstance, "IsJobComplete");
            if (isCompleteResult.error) {
                validation.error = `IsJobComplete failed: ${isCompleteResult.error}`;
                return validation;
            }

            validation.isCompleted = isCompleteResult.value === true;

            // Check if can collect (indicates completed state)
            const canCollectResult = this.safeInvoke(goodyHutInstance, "CanCollect");
            if (!canCollectResult.error && canCollectResult.value === true) {
                validation.state = "COMPLETED_AWAITING";
            } else if (validation.isCompleted) {
                validation.state = "COMPLETED";
            } else {
                validation.state = "COLLECTING";
            }

            // Get reward information with multiple method attempts
            const rewardTypeMethods = [
                "GetRewardType", "GetReward", "GetResourceType", "GetCollectibleType",
                "GetLootType", "GetDropType", "GetItemType", "GetRewardResourceType"
            ];
            const rewardAmountMethods = [
                "GetRewardAmount", "GetAmount", "GetResourceAmount", "GetCollectibleAmount",
                "GetLootAmount", "GetDropAmount", "GetItemAmount", "GetRewardResourceAmount"
            ];

            // Try multiple methods for reward type
            for (const methodName of rewardTypeMethods) {
                const result = this.safeInvoke(goodyHutInstance, methodName);
                if (!result.error && result.value !== null && result.value !== undefined) {
                    validation.rewardType = result.value.toString();
                    break;
                }
            }

            // Try multiple methods for reward amount
            for (const methodName of rewardAmountMethods) {
                const result = this.safeInvoke(goodyHutInstance, methodName);
                if (!result.error && result.value !== null && result.value !== undefined) {
                    validation.rewardAmount = Number(result.value) || 0;
                    break;
                }
            }

            // If still unknown, try to get basic info from the instance
            if (validation.rewardType === "UNKNOWN") {
                const nameResult = this.safeInvoke(goodyHutInstance, "GetName");
                if (!nameResult.error && nameResult.value) {
                    validation.rewardType = nameResult.value.toString();
                }
            }

            // Check for sellable ruins (multiple methods to try)
            const ruinCheckMethods = ["HasRuins", "HasDebris", "CanSell", "CanClear"];
            for (const methodName of ruinCheckMethods) {
                const result = this.safeInvoke(goodyHutInstance, methodName);
                if (!result.error && result.value === true) {
                    validation.hasRuins = true;
                    validation.canSell = true;
                    break;
                }
            }

            // Instance is valid if it has a GoodyHut and is completed with potential ruins
            validation.isValid = validation.hasGoodyHut && 
                                (validation.isCompleted || validation.state === "COMPLETED_AWAITING");

            return validation;

        } catch (error) {
            validation.error = `Validation error: ${error}`;
            return validation;
        }
    }

    /**
     * Execute SellRuins method on a validated instance with retry logic
     */
    private async executeSellRuins(instance: any, validation: InstanceValidation, index: number): Promise<boolean> {
        const maxRetries = 3;
        let retryCount = 0;

        while (retryCount < maxRetries) {
            try {
                console.log(`🗑️ [${index}] Attempting to sell ruins (attempt ${retryCount + 1}/${maxRetries})...`);

                // Re-validate instance before each attempt
                if (!this.isValidInstance(instance)) {
                    console.log(`❌ [${index}] Instance became invalid during processing`);
                    return false;
                }

                // Try multiple field names for GoodyHut component
                const goodyHutFieldNames = ["m_goodyHut", "goodyHut", "_goodyHut", "m_GoodyHut"];
                let goodyHutInstance: any = null;

                for (const fieldName of goodyHutFieldNames) {
                    try {
                        const goodyHutField = instance.field(fieldName);
                        if (goodyHutField && goodyHutField.value && goodyHutField.value.toString() !== "0x0") {
                            goodyHutInstance = goodyHutField.value;
                            break;
                        }
                    } catch (fieldError) {
                        continue;
                    }
                }

                if (!goodyHutInstance || !this.isValidInstance(goodyHutInstance)) {
                    console.log(`❌ [${index}] GoodyHut component not accessible or invalid`);
                    return false;
                }

                // Direct cleanup approach - bypass failing SellRuins method
                // Based on analysis: SellRuins has 100% failure rate with access violations
                // Update/Reset sequence achieves the same result without errors

                console.log(`🔧 [${index}] Using direct cleanup approach (bypassing SellRuins)`);

                const cleanupSuccess = await this.executeDirectCleanup(goodyHutInstance, index);

                if (cleanupSuccess) {
                    console.log(`✅ [${index}] Direct cleanup completed successfully`);

                    // Quick verification - check for state change
                    await new Promise(resolve => setTimeout(resolve, 100));
                    const quickValidation = this.validateInstance(instance, index);

                    if (quickValidation.state !== validation.state || quickValidation.state.includes("AWAITING")) {
                        console.log(`🎉 [${index}] State changed: ${validation.state} → ${quickValidation.state} - Entity processed`);
                        return true;
                    } else {
                        console.log(`⚠️ [${index}] No state change detected after cleanup`);
                        return false;
                    }
                } else {
                    console.log(`❌ [${index}] Direct cleanup failed`);
                    return false;
                }

                // Direct cleanup failed, increment retry count
                retryCount++;
                this.stats.retryAttempts++;

                if (retryCount < maxRetries) {
                    console.log(`🔄 [${index}] Retrying direct cleanup (${retryCount}/${maxRetries})...`);
                    // Wait before retry with exponential backoff
                    await new Promise(resolve => setTimeout(resolve, 100 * retryCount));
                }

            } catch (error) {
                console.log(`❌ [${index}] Sell execution error: ${error}`);
                retryCount++;
                this.stats.retryAttempts++;

                if (retryCount < maxRetries) {
                    await new Promise(resolve => setTimeout(resolve, 100 * retryCount));
                }
            }
        }

        console.log(`❌ [${index}] All retry attempts failed`);
        return false;
    }

    /**
     * Execute direct cleanup approach - bypasses failing SellRuins method
     * Uses proven Update/Reset sequence that consistently works
     */
    private async executeDirectCleanup(goodyHutInstance: any, index: number): Promise<boolean> {
        try {
            console.log(`🔧 [${index}] Executing direct cleanup (Update → Reset)`);

            // Comprehensive pre-call validation
            if (!this.isValidInstance(goodyHutInstance)) {
                console.log(`❌ [${index}] GoodyHutHelper instance invalid before cleanup`);
                return false;
            }

            // Execute the proven Update/Reset sequence
            const updateResult = this.safeInvoke(goodyHutInstance, "Update");
            if (updateResult.error) {
                console.log(`⚠️ [${index}] Update failed: ${updateResult.error}`);
                return false;
            }
            console.log(`✅ [${index}] Update executed successfully`);

            const resetResult = this.safeInvoke(goodyHutInstance, "Reset");
            if (resetResult.error) {
                console.log(`⚠️ [${index}] Reset failed: ${resetResult.error}`);
                return false;
            }
            console.log(`✅ [${index}] Reset executed successfully`);

            // Both methods succeeded
            return true;

        } catch (error) {
            console.log(`❌ [${index}] Direct cleanup error: ${error}`);
            return false;
        }
    }

    /**
     * Attempt SellRuins() with maximum safety measures and fallback options
     */
    private async attemptSellRuinsWithMaxSafety(goodyHutInstance: any, index: number): Promise<boolean> {
        try {
            console.log(`🔧 [${index}] Attempting SellRuins() - the only method that actually removes ruins`);

            // Multiple validation layers before attempting SellRuins()
            if (!goodyHutInstance) {
                console.log(`❌ [${index}] GoodyHut instance is null`);
                return false;
            }

            // Enhanced instance validation with multiple attempts
            let validationAttempts = 0;
            const maxValidationAttempts = 3;

            while (validationAttempts < maxValidationAttempts) {
                if (this.isValidInstance(goodyHutInstance)) {
                    break;
                }

                validationAttempts++;
                if (validationAttempts < maxValidationAttempts) {
                    console.log(`⚠️ [${index}] Instance validation failed, retrying... (${validationAttempts}/${maxValidationAttempts})`);
                    await new Promise(resolve => setTimeout(resolve, 100));
                } else {
                    console.log(`❌ [${index}] Instance validation failed after ${maxValidationAttempts} attempts`);
                    this.stats.accessViolations++;
                    return false;
                }
            }

            // Attempt SellRuins() with maximum protection
            console.log(`🗑️ [${index}] Calling SellRuins() method...`);

            try {
                const sellResult = this.safeInvoke(goodyHutInstance, "SellRuins");

                if (sellResult.error) {
                    const errorMsg = sellResult.error;
                    console.log(`❌ [${index}] SellRuins() failed: ${errorMsg}`);

                    if (errorMsg.includes("access violation") || errorMsg.includes("0x0")) {
                        console.log(`🚨 [${index}] Access violation in SellRuins() - this is expected`);
                        this.stats.accessViolations++;

                        // Try fallback approach
                        console.log(`🔄 [${index}] Attempting fallback cleanup methods...`);
                        return await this.executeDirectCleanupSafe(goodyHutInstance, index);
                    } else {
                        console.log(`⚠️ [${index}] Non-access violation error: ${errorMsg}`);
                        return false;
                    }
                } else {
                    console.log(`✅ [${index}] SellRuins() executed successfully!`);

                    // Add a small delay for the game to process the change
                    await new Promise(resolve => setTimeout(resolve, 100));

                    return true;
                }

            } catch (sellError: any) {
                const sellErrorMsg = String(sellError);
                console.log(`❌ [${index}] SellRuins() exception: ${sellErrorMsg}`);

                if (sellErrorMsg.includes("access violation") || sellErrorMsg.includes("0x0")) {
                    console.log(`🚨 [${index}] Access violation exception in SellRuins()`);
                    this.stats.accessViolations++;

                    // Try fallback approach
                    console.log(`🔄 [${index}] Attempting fallback cleanup methods...`);
                    return await this.executeDirectCleanupSafe(goodyHutInstance, index);
                } else {
                    console.log(`❌ [${index}] Unexpected SellRuins() exception: ${sellErrorMsg}`);
                    return false;
                }
            }

        } catch (error: any) {
            const errorMsg = String(error);
            console.log(`❌ [${index}] SellRuins attempt error: ${errorMsg}`);

            if (errorMsg.includes("access violation") || errorMsg.includes("0x0")) {
                this.stats.accessViolations++;
            }

            return false;
        }
    }

    /**
     * Execute direct cleanup with enhanced safety checks and error recovery (FALLBACK ONLY)
     */
    private async executeDirectCleanupSafe(goodyHutInstance: any, index: number): Promise<boolean> {
        try {
            console.log(`🔧 [${index}] Executing safe direct cleanup with enhanced validation`);

            // Multiple validation layers before attempting cleanup
            if (!goodyHutInstance) {
                console.log(`❌ [${index}] GoodyHut instance is null`);
                return false;
            }

            // Enhanced instance validation with retry
            let validationAttempts = 0;
            const maxValidationAttempts = 3;

            while (validationAttempts < maxValidationAttempts) {
                if (this.isValidInstance(goodyHutInstance)) {
                    break;
                }

                validationAttempts++;
                if (validationAttempts < maxValidationAttempts) {
                    console.log(`⚠️ [${index}] Instance validation failed, retrying... (${validationAttempts}/${maxValidationAttempts})`);
                    await new Promise(resolve => setTimeout(resolve, 100));
                } else {
                    console.log(`❌ [${index}] Instance validation failed after ${maxValidationAttempts} attempts`);
                    this.stats.accessViolations++;
                    return false;
                }
            }

            // Execute cleanup methods with individual error handling
            let cleanupSuccess = false;

            // Try Update method with enhanced error handling
            try {
                const updateResult = this.safeInvoke(goodyHutInstance, "Update");
                if (updateResult.error) {
                    if (updateResult.error.includes("access violation") || updateResult.error.includes("0x0")) {
                        console.log(`❌ [${index}] Update method caused access violation: ${updateResult.error}`);
                        this.stats.accessViolations++;
                        return false;
                    } else {
                        console.log(`⚠️ [${index}] Update failed (non-critical): ${updateResult.error}`);
                    }
                } else {
                    console.log(`✅ [${index}] Update executed successfully`);
                    cleanupSuccess = true;
                }
            } catch (updateError: any) {
                console.log(`❌ [${index}] Update method exception: ${updateError}`);
                if (String(updateError).includes("access violation")) {
                    this.stats.accessViolations++;
                    return false;
                }
            }

            // Small delay between method calls
            await new Promise(resolve => setTimeout(resolve, 50));

            // Re-validate instance before Reset
            if (!this.isValidInstance(goodyHutInstance)) {
                console.log(`❌ [${index}] Instance became invalid after Update`);
                this.stats.accessViolations++;
                return false;
            }

            // Try Reset method with enhanced error handling
            try {
                const resetResult = this.safeInvoke(goodyHutInstance, "Reset");
                if (resetResult.error) {
                    if (resetResult.error.includes("access violation") || resetResult.error.includes("0x0")) {
                        console.log(`❌ [${index}] Reset method caused access violation: ${resetResult.error}`);
                        this.stats.accessViolations++;
                        return false;
                    } else {
                        console.log(`⚠️ [${index}] Reset failed (non-critical): ${resetResult.error}`);
                    }
                } else {
                    console.log(`✅ [${index}] Reset executed successfully`);
                    cleanupSuccess = true;
                }
            } catch (resetError: any) {
                console.log(`❌ [${index}] Reset method exception: ${resetError}`);
                if (String(resetError).includes("access violation")) {
                    this.stats.accessViolations++;
                    return false;
                }
            }

            return cleanupSuccess;

        } catch (error: any) {
            const errorMsg = String(error);
            console.log(`❌ [${index}] Safe cleanup error: ${errorMsg}`);

            if (errorMsg.includes("access violation") || errorMsg.includes("0x0")) {
                this.stats.accessViolations++;
            }

            return false;
        }
    }

    /**
     * Perform post-SellRuins processing to trigger state machine updates and cleanup
     */
    private async performPostSellRuinsProcessing(instance: any, goodyHutInstance: any, index: number): Promise<boolean> {
        try {
            console.log(`🔧 [${index}] Performing post-SellRuins processing...`);
            let processedSuccessfully = false;

            // Step 1: Try to access and update the state machine (m_stateMachine field at offset 0x18)
            try {
                const stateMachineField = goodyHutInstance.field("m_stateMachine");
                if (stateMachineField && stateMachineField.value && this.isValidInstance(stateMachineField.value)) {
                    const stateMachine = stateMachineField.value;
                    console.log(`🔧 [${index}] Found state machine, attempting to trigger state update...`);

                    // Try to call Update methods on the state machine
                    const updateMethods = ["Update", "FixedUpdate", "ProcessState", "TriggerTransition"];
                    for (const methodName of updateMethods) {
                        const result = this.safeInvoke(stateMachine, methodName);
                        if (!result.error) {
                            console.log(`✅ [${index}] Successfully called ${methodName} on state machine`);
                            processedSuccessfully = true;
                        }
                    }
                }
            } catch (error) {
                console.log(`⚠️ [${index}] State machine processing failed: ${error}`);
            }

            // Step 2: Call essential update methods only (streamlined for speed)
            const essentialMethods = ["Update", "FixedUpdate", "Reset"];
            for (const methodName of essentialMethods) {
                const result = this.safeInvoke(goodyHutInstance, methodName);
                if (!result.error) {
                    console.log(`✅ [${index}] Successfully called ${methodName} on GoodyHutHelper`);
                    processedSuccessfully = true;
                }
            }

            // Step 3: Minimal wait for processing to complete (optimized for speed)
            if (processedSuccessfully) {
                await new Promise(resolve => setTimeout(resolve, 50));
            }

            return processedSuccessfully;

        } catch (error) {
            console.log(`❌ [${index}] Post-SellRuins processing error: ${error}`);
            return false;
        }
    }

    /**
     * Enumerate available methods on a GoodyHutHelper instance for debugging
     */
    private enumerateAvailableMethods(goodyHutInstance: any, index: number): void {
        try {
            console.log(`🔍 [${index}] Enumerating available methods on GoodyHutHelper instance:`);

            if (!this.isValidInstance(goodyHutInstance)) {
                console.log(`⚠️ [${index}] Cannot enumerate methods - invalid instance`);
                return;
            }

            // Try to get the class information
            const instanceClass = goodyHutInstance.class;
            if (!instanceClass) {
                console.log(`⚠️ [${index}] Cannot get class information`);
                return;
            }

            console.log(`📋 [${index}] Class name: ${instanceClass.name}`);

            // Try to enumerate methods if available
            if (instanceClass.methods && instanceClass.methods.length > 0) {
                console.log(`📋 [${index}] Available methods (${instanceClass.methods.length} total):`);
                const relevantMethods = instanceClass.methods.filter((method: any) =>
                    method.name && (
                        method.name.toLowerCase().includes('sell') ||
                        method.name.toLowerCase().includes('clear') ||
                        method.name.toLowerCase().includes('ruin') ||
                        method.name.toLowerCase().includes('debris') ||
                        method.name.toLowerCase().includes('update') ||
                        method.name.toLowerCase().includes('reset')
                    )
                );

                if (relevantMethods.length > 0) {
                    relevantMethods.forEach((method: any) => {
                        console.log(`   - ${method.name}`);
                    });
                } else {
                    console.log(`   No relevant methods found (sell/clear/ruin/debris/update/reset)`);
                    // Show first 10 methods for debugging
                    instanceClass.methods.slice(0, 10).forEach((method: any) => {
                        console.log(`   - ${method.name} (general)`);
                    });
                }
            } else {
                console.log(`⚠️ [${index}] No methods available or methods not accessible`);
            }

        } catch (error) {
            console.log(`❌ [${index}] Method enumeration failed: ${error}`);
        }
    }

    /**
     * Verify that an entity has been successfully removed from the game world
     */
    private async verifyEntityRemoval(instance: any, goodyHutInstance: any, originalValidation: InstanceValidation, index: number): Promise<{success: boolean, reason: string}> {
        try {
            // Wait for the game to process the removal (optimized timing)
            await new Promise(resolve => setTimeout(resolve, 1000));

            // Check if the instance is still valid and accessible
            if (!this.isValidInstance(instance)) {
                return { success: true, reason: "Instance no longer accessible (likely removed)" };
            }

            // Re-validate the instance to see if its state changed
            const postValidation = this.validateInstance(instance, index);

            // Check if the instance no longer has a GoodyHut component
            if (!postValidation.hasGoodyHut) {
                return { success: true, reason: "GoodyHut component removed" };
            }

            // Check if the GoodyHut instance itself is no longer valid
            if (!this.isValidInstance(goodyHutInstance)) {
                return { success: true, reason: "GoodyHut instance no longer accessible" };
            }

            // Check if the state changed significantly
            if (postValidation.state !== originalValidation.state) {
                console.log(`📊 [${index}] State changed: ${originalValidation.state} → ${postValidation.state}`);

                // If it's no longer completed or no longer has ruins, consider it successful
                if (!postValidation.isCompleted || !postValidation.hasRuins) {
                    return { success: true, reason: `State changed to ${postValidation.state}` };
                }

                // Check for specific state transitions that indicate removal
                if (postValidation.state.includes("CLEANING") || postValidation.state.includes("REMOVED") ||
                    postValidation.state.includes("CLEARED") || postValidation.state.includes("EMPTY")) {
                    return { success: true, reason: `State indicates removal: ${postValidation.state}` };
                }
            }

            // Additional checks for entity removal indicators
            // Check if reward information is no longer available (indicates processing)
            if (originalValidation.rewardAmount !== null && postValidation.rewardAmount === null) {
                return { success: true, reason: "Reward information cleared (indicates processing)" };
            }

            // Check if the entity is no longer in a "completed" state
            if (originalValidation.isCompleted && !postValidation.isCompleted) {
                return { success: true, reason: "Entity no longer in completed state" };
            }

            // Try to check if the entity is still discoverable in a fresh entity search
            const currentEntityCount = this.getCurrentEntityCount();
            if (currentEntityCount >= 0 && currentEntityCount < this.stats.totalInstances) {
                // Entity count has decreased, which is a good sign
                return { success: true, reason: `Entity count decreased (${this.stats.totalInstances} → ${currentEntityCount})` };
            }

            // If we get here, the entity appears to still exist in the same state
            return { success: false, reason: `Entity still exists: ${postValidation.state} (${postValidation.rewardType}: ${postValidation.rewardAmount})` };

        } catch (error) {
            // If we can't access the instance, it might have been removed
            return { success: true, reason: `Verification error (likely removed): ${error}` };
        }
    }

    /**
     * Perform additional cleanup operations to ensure entity removal
     */
    private async performCleanupOperations(instance: any, goodyHutInstance: any, index: number): Promise<boolean> {
        try {
            console.log(`🔧 [${index}] Performing cleanup operations...`);

            // Try to set cleanup flags (based on user's memory about GoodyHutHelperConfig)
            this.setCleanupFlags(goodyHutInstance, index);

            // Try validation/refresh methods
            const validationMethods = [
                "Validate", "Refresh", "Update", "ProcessCleanup",
                "TriggerCleanup", "ForceUpdate", "Invalidate", "Reset"
            ];

            let cleanupSuccess = false;
            for (const methodName of validationMethods) {
                const result = this.safeInvoke(goodyHutInstance, methodName);
                if (!result.error) {
                    console.log(`✅ [${index}] Successfully executed cleanup method: ${methodName}`);
                    cleanupSuccess = true;
                }
            }

            // Try calling cleanup on the parent instance as well
            const parentCleanupMethods = ["ProcessCleanup", "TriggerValidation", "UpdateState", "Refresh"];
            for (const methodName of parentCleanupMethods) {
                const result = this.safeInvoke(instance, methodName);
                if (!result.error) {
                    console.log(`✅ [${index}] Successfully executed parent cleanup method: ${methodName}`);
                    cleanupSuccess = true;
                }
            }

            if (cleanupSuccess) {
                // Wait for cleanup to process
                await new Promise(resolve => setTimeout(resolve, 1500));

                // Verify the cleanup worked
                const verificationResult = await this.verifyEntityRemoval(instance, goodyHutInstance,
                    { state: "CLEANUP_ATTEMPTED" } as InstanceValidation, index);
                return verificationResult.success;
            }

            return false;

        } catch (error) {
            console.log(`❌ [${index}] Cleanup operations error: ${error}`);
            return false;
        }
    }

    /**
     * Set cleanup flags on GoodyHutHelperConfig (based on user's automation preferences)
     */
    private setCleanupFlags(goodyHutInstance: any, index: number): boolean {
        try {
            // Try to access GoodyHutHelperConfig or similar configuration objects
            const configFieldNames = ["m_config", "config", "_config", "m_helperConfig", "helperConfig"];

            for (const fieldName of configFieldNames) {
                try {
                    const configField = goodyHutInstance.field(fieldName);
                    if (configField && configField.value && this.isValidInstance(configField.value)) {
                        const configInstance = configField.value;

                        // Try to set cleanup flag at offset 0x30 (as mentioned in user's memory)
                        try {
                            const cleanupField = configInstance.field("cleanUp");
                            if (cleanupField) {
                                cleanupField.value = true;
                                console.log(`✅ [${index}] Set cleanUp flag via field access`);
                                return true;
                            }
                        } catch (fieldError) {
                            // Try direct memory access at offset 0x30
                            try {
                                configInstance.handle.add(0x30).writeU8(1);
                                console.log(`✅ [${index}] Set cleanUp flag via memory offset 0x30`);
                                return true;
                            } catch (memError) {
                                // Continue to next config field
                            }
                        }
                    }
                } catch (error) {
                    continue;
                }
            }

            // Try setting cleanup flags on the goodyHutInstance itself
            const cleanupFlagNames = ["cleanUp", "m_cleanUp", "_cleanUp", "shouldCleanup", "needsCleanup"];
            for (const flagName of cleanupFlagNames) {
                try {
                    const flagField = goodyHutInstance.field(flagName);
                    if (flagField) {
                        flagField.value = true;
                        console.log(`✅ [${index}] Set ${flagName} flag on GoodyHut instance`);
                        return true;
                    }
                } catch (error) {
                    continue;
                }
            }

            return false;

        } catch (error) {
            console.log(`⚠️ [${index}] Failed to set cleanup flags: ${error}`);
            return false;
        }
    }

    /**
     * Discover all EntityController instances using multiple strategies
     */
    private discoverInstances(): any[] {
        try {
            console.log("🔍 Discovering EntityController instances...");

            let entityInstances = Il2Cpp.gc.choose(this.entityControllerClass);

            if (!entityInstances || entityInstances.length === 0) {
                console.log("⚠️ No instances found with primary class, trying alternative discovery...");

                // Try discovering instances of related classes
                const alternativeClasses = [
                    "GoodyHut",
                    "Building",
                    "Structure",
                    "Collectible",
                    "GameObject"
                ];

                for (const altClassName of alternativeClasses) {
                    try {
                        const altClass = this.assemblyImage.class(altClassName);
                        if (altClass) {
                            const altInstances = Il2Cpp.gc.choose(altClass);
                            console.log(`🔍 Found ${altInstances.length} ${altClassName} instances`);

                            if (altInstances.length > 0) {
                                // Filter instances that might have GoodyHut components
                                const filteredInstances = altInstances.filter((instance: any) => {
                                    try {
                                        const goodyHutField = instance.field("m_goodyHut") ||
                                                            instance.field("goodyHut") ||
                                                            instance.field("_goodyHut");
                                        return goodyHutField && goodyHutField.value && goodyHutField.value.toString() !== "0x0";
                                    } catch {
                                        return false;
                                    }
                                });

                                if (filteredInstances.length > 0) {
                                    console.log(`✅ Found ${filteredInstances.length} ${altClassName} instances with GoodyHut components`);
                                    entityInstances = filteredInstances;
                                    break;
                                }
                            }
                        }
                    } catch (error) {
                        console.log(`⚠️ Failed to check ${altClassName}: ${error}`);
                    }
                }
            }

            if (!entityInstances || entityInstances.length === 0) {
                console.log("❌ No suitable instances found with any discovery method");
                return [];
            }

            console.log(`✅ Discovered ${entityInstances.length} instances for processing`);
            this.stats.totalInstances = entityInstances.length;

            return entityInstances;

        } catch (error) {
            console.log(`❌ Instance discovery failed: ${error}`);
            return [];
        }
    }

    /**
     * Process a batch of entities with enhanced safety and error recovery
     */
    private async processBatch(batch: { instance: any, validation: InstanceValidation, index: number }[], batchNumber: number, totalBatches: number): Promise<{successful: number, failed: number, total: number}> {
        console.log(`🚀 Starting safe processing of batch ${batchNumber}/${totalBatches} (${batch.length} entities)`);

        const results = {
            successful: 0,
            failed: 0,
            total: batch.length
        };

        // Process entities sequentially to prevent race conditions and access violations
        for (let batchIndex = 0; batchIndex < batch.length; batchIndex++) {
            const { instance, validation, index } = batch[batchIndex];

            try {
                this.stats.ruinSellAttempts++;

                // Pre-processing validation with comprehensive null checks
                if (!this.isValidInstance(instance)) {
                    console.log(`❌ [${index}] Instance became invalid before processing`);
                    this.stats.accessViolations++;
                    results.failed++;
                    continue;
                }

                // Get GoodyHut instance with enhanced validation
                const goodyHutFieldNames = ["m_goodyHut", "goodyHut", "_goodyHut", "m_GoodyHut"];
                let goodyHutInstance: any = null;

                for (const fieldName of goodyHutFieldNames) {
                    try {
                        // Additional null check before field access
                        if (!instance || !instance.field) {
                            break;
                        }

                        const goodyHutField = instance.field(fieldName);
                        if (goodyHutField && goodyHutField.value) {
                            // Enhanced null pointer validation
                            const fieldValueStr = goodyHutField.value.toString();
                            if (fieldValueStr !== "0x0" && fieldValueStr !== "null" && fieldValueStr !== "(nil)") {
                                // Validate the GoodyHut instance before using it
                                if (this.isValidInstance(goodyHutField.value)) {
                                    goodyHutInstance = goodyHutField.value;
                                    break;
                                }
                            }
                        }
                    } catch (fieldError) {
                        // Log field access errors for debugging
                        const fieldErrorMsg = String(fieldError);
                        if (fieldErrorMsg.includes("access violation") || fieldErrorMsg.includes("0x0")) {
                            console.log(`⚠️ [${index}] Access violation getting field ${fieldName}: ${fieldErrorMsg}`);
                            this.stats.accessViolations++;
                        }
                        continue;
                    }
                }

                if (!goodyHutInstance) {
                    console.log(`❌ [${index}] No valid GoodyHut component found`);
                    results.failed++;
                    continue;
                }

                // Final validation before method invocation
                if (!this.isValidInstance(goodyHutInstance)) {
                    console.log(`❌ [${index}] GoodyHut instance invalid after retrieval`);
                    this.stats.accessViolations++;
                    results.failed++;
                    continue;
                }

                // Attempt SellRuins() first - it's the only method that actually works
                console.log(`🔧 [${index}] Attempting SellRuins() with enhanced safety...`);

                const cleanupSuccess = await this.attemptSellRuinsWithMaxSafety(goodyHutInstance, index);

                if (cleanupSuccess) {
                    console.log(`✅ [${index}] Batch ${batchNumber} entity ${batchIndex + 1}/${batch.length} processed successfully`);
                    this.stats.successfulSells++;
                    results.successful++;
                } else {
                    console.log(`❌ [${index}] Cleanup failed for entity ${batchIndex + 1}/${batch.length}`);
                    this.stats.failedSells++;
                    results.failed++;
                }

                // Small delay between entities to prevent overwhelming the game engine
                await new Promise(resolve => setTimeout(resolve, 50));

            } catch (error: any) {
                const errorMsg = String(error);
                console.log(`❌ [${index}] Batch processing error: ${errorMsg}`);

                // Track specific error types
                if (errorMsg.includes("access violation") || errorMsg.includes("0x0")) {
                    this.stats.accessViolations++;
                } else if (errorMsg.includes("connection") || errorMsg.includes("device")) {
                    this.stats.connectionErrors++;
                }

                results.failed++;

                // If we're getting access violations, add a longer delay
                if (errorMsg.includes("access violation")) {
                    console.log(`⚠️ [${index}] Access violation detected, adding recovery delay...`);
                    await new Promise(resolve => setTimeout(resolve, 200));
                }
            }
        }

        const successRate = results.total > 0 ? ((results.successful / results.total) * 100).toFixed(1) : "0.0";
        console.log(`🎯 Batch ${batchNumber} Results: ${results.successful}/${results.total} successful (${successRate}% success rate)`);

        // Check if we should switch to emergency mode due to high access violation rate
        const accessViolationRate = results.total > 0 ? (results.failed / results.total) : 0;
        if (accessViolationRate > 0.8) { // More than 80% failures
            console.log(`⚠️ High failure rate detected (${(accessViolationRate * 100).toFixed(1)}%) - consider switching to emergency mode`);
        }

        // Wait for game to process all operations before next batch
        await new Promise(resolve => setTimeout(resolve, 1000));

        return results;
    }

    /**
     * Emergency processing mode - ultra-safe sequential processing with extensive validation
     */
    private async processEmergencyMode(validInstances: { instance: any, validation: InstanceValidation, index: number }[]): Promise<void> {
        console.log(`🚨 Entering emergency processing mode for ${validInstances.length} entities`);
        console.log(`🔧 Using ultra-safe sequential processing with extended delays`);

        let successCount = 0;
        let failureCount = 0;

        for (let i = 0; i < validInstances.length; i++) {
            const { instance, validation, index } = validInstances[i];

            try {
                console.log(`🔧 [${index}] Emergency processing entity ${i + 1}/${validInstances.length}`);

                // Extended pre-validation
                if (!this.isValidInstance(instance)) {
                    console.log(`❌ [${index}] Instance invalid in emergency mode`);
                    failureCount++;
                    continue;
                }

                // Get GoodyHut with maximum safety
                let goodyHutInstance: any = null;
                const goodyHutFieldNames = ["m_goodyHut", "goodyHut", "_goodyHut", "m_GoodyHut"];

                for (const fieldName of goodyHutFieldNames) {
                    try {
                        if (!instance || !instance.field) break;

                        const goodyHutField = instance.field(fieldName);
                        if (goodyHutField && goodyHutField.value) {
                            const fieldValueStr = goodyHutField.value.toString();
                            if (fieldValueStr !== "0x0" && fieldValueStr !== "null" && fieldValueStr !== "(nil)") {
                                if (this.isValidInstance(goodyHutField.value)) {
                                    goodyHutInstance = goodyHutField.value;
                                    break;
                                }
                            }
                        }
                    } catch (fieldError) {
                        console.log(`⚠️ [${index}] Emergency mode field access error: ${fieldError}`);
                        continue;
                    }
                }

                if (!goodyHutInstance) {
                    console.log(`❌ [${index}] No GoodyHut found in emergency mode`);
                    failureCount++;
                    continue;
                }

                // Even in emergency mode, we need to try SellRuins() - it's the only method that works
                try {
                    console.log(`🗑️ [${index}] Emergency mode: Attempting SellRuins()...`);
                    const sellResult = this.safeInvoke(goodyHutInstance, "SellRuins");

                    if (!sellResult.error) {
                        console.log(`✅ [${index}] Emergency SellRuins() successful`);
                        successCount++;
                        this.stats.successfulSells++;
                    } else {
                        console.log(`❌ [${index}] Emergency SellRuins() failed: ${sellResult.error}`);

                        // In emergency mode, if SellRuins fails, try the fallback
                        console.log(`🔄 [${index}] Emergency fallback: trying Update() method...`);
                        const updateResult = this.safeInvoke(goodyHutInstance, "Update");
                        if (!updateResult.error) {
                            console.log(`⚠️ [${index}] Emergency Update() executed (may not remove ruins)`);
                        }

                        failureCount++;
                        this.stats.failedSells++;
                    }
                } catch (cleanupError: any) {
                    console.log(`❌ [${index}] Emergency mode exception: ${cleanupError}`);
                    failureCount++;
                    this.stats.failedSells++;
                }

                // Extended delay between entities in emergency mode
                await new Promise(resolve => setTimeout(resolve, 500));

                // Progress update every 10 entities
                if ((i + 1) % 10 === 0) {
                    const progress = ((i + 1) / validInstances.length * 100).toFixed(1);
                    console.log(`📊 Emergency mode progress: ${progress}% (${successCount} successes, ${failureCount} failures)`);
                }

            } catch (error) {
                console.log(`❌ [${index}] Emergency mode error: ${error}`);
                failureCount++;

                // Extended recovery delay on errors
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
        }

        const totalProcessed = successCount + failureCount;
        const emergencySuccessRate = totalProcessed > 0 ? ((successCount / totalProcessed) * 100).toFixed(1) : "0.0";
        console.log(`🚨 Emergency mode completed: ${successCount}/${totalProcessed} successful (${emergencySuccessRate}% success rate)`);
    }

    /**
     * Get current entity count for progress tracking
     */
    private getCurrentEntityCount(): number {
        try {
            const currentInstances = this.discoverInstances();
            return currentInstances.length;
        } catch (error) {
            console.log(`⚠️ Failed to get current entity count: ${error}`);
            return -1;
        }
    }

    /**
     * Process all instances for ruin selling with dynamic progress tracking
     */
    private async processAllInstances(): Promise<void> {
        let initialInstances = this.discoverInstances();
        if (initialInstances.length === 0) {
            return;
        }

        const initialCount = initialInstances.length;
        console.log(`🎯 Starting with ${initialCount} total entities`);

        let processedCount = 0;
        let actuallyRemovedCount = 0;
        let lastEntityCountCheck = initialCount;

        console.log("🔍 Filtering instances for GoodyHut components with sellable ruins...");
        const validInstances: { instance: any, validation: InstanceValidation, index: number }[] = [];

        // First pass: validate all instances
        for (let i = 0; i < initialInstances.length; i++) {
            const instance = initialInstances[i];
            const validation = this.validateInstance(instance, i);

            if (validation.hasGoodyHut) {
                this.stats.validGoodyHuts++;
            }

            if (validation.isCompleted) {
                this.stats.completedInstances++;
            }

            // Include instances that are completed and potentially have ruins
            if (validation.isValid && (validation.hasRuins || validation.isCompleted)) {
                validInstances.push({ instance, validation, index: i });
                console.log(`📋 [${i}] Queued RUIN: ${validation.state} (TagEnum: ${validation.rewardType}, Amount: ${validation.rewardAmount})`);
            } else if (validation.error && validation.error.includes("Not a ruin entity")) {
                // Log non-ruin entities for debugging (but don't spam too much)
                if (i % 100 === 0) {
                    console.log(`🚫 [${i}] Skipped non-ruin: ${validation.error}`);
                }
            }

            // Progress update every 1000 instances
            if ((i + 1) % 1000 === 0) {
                const progress = ((i + 1) / initialInstances.length * 100).toFixed(1);
                console.log(`📊 Validation progress: ${progress}% (${i + 1}/${initialInstances.length})`);
            }
        }

        console.log(`🎯 Found ${validInstances.length} instances ready for ruin selling`);
        console.log(`📊 Summary: ${this.stats.validGoodyHuts} GoodyHuts, ${this.stats.completedInstances} completed`);

        if (validInstances.length === 0) {
            console.log("ℹ️ No instances found with sellable ruins");
            return;
        }

        // Second pass: execute ruin selling with adaptive processing strategy
        console.log("🗑️ Starting adaptive ruin selling operations...");

        const batchSize = 100; // Process 100 entities per batch for maximum speed
        const totalBatches = Math.ceil(validInstances.length / batchSize);
        let consecutiveFailures = 0;
        let emergencyModeTriggered = false;

        for (let batchIndex = 0; batchIndex < totalBatches; batchIndex++) {
            const batchStart = batchIndex * batchSize;
            const batchEnd = Math.min(batchStart + batchSize, validInstances.length);
            const currentBatch = validInstances.slice(batchStart, batchEnd);

            console.log(`\n🔥 Processing Batch ${batchIndex + 1}/${totalBatches} (${currentBatch.length} entities)`);

            // Check connection health before each batch
            if (!this.checkConnectionHealth()) {
                console.log("⚠️ Connection unhealthy, attempting recovery...");
                const recovered = await this.attemptConnectionRecovery();
                if (!recovered) {
                    console.log("❌ Connection recovery failed, stopping processing");
                    break;
                }
            }

            // Check if we should trigger emergency mode due to high access violation rate
            const currentAccessViolationRate = this.stats.ruinSellAttempts > 0 ?
                (this.stats.accessViolations / this.stats.ruinSellAttempts) : 0;

            if (currentAccessViolationRate > 0.7 && !emergencyModeTriggered) {
                console.log(`🚨 High access violation rate detected (${(currentAccessViolationRate * 100).toFixed(1)}%)`);
                console.log(`🚨 Switching to emergency mode for remaining ${validInstances.length - batchStart} entities`);

                emergencyModeTriggered = true;
                const remainingInstances = validInstances.slice(batchStart);
                await this.processEmergencyMode(remainingInstances);
                break;
            }

            // Process batch with enhanced error tracking
            const batchResults = await this.processBatch(currentBatch, batchIndex + 1, totalBatches);

            // Track consecutive failures for emergency mode trigger
            if (batchResults.successful === 0 && batchResults.failed > 0) {
                consecutiveFailures++;
                console.log(`⚠️ Batch had zero successes (consecutive failures: ${consecutiveFailures})`);

                if (consecutiveFailures >= 3) {
                    console.log(`🚨 Three consecutive failed batches detected - switching to emergency mode`);
                    emergencyModeTriggered = true;
                    const remainingInstances = validInstances.slice(batchEnd);
                    if (remainingInstances.length > 0) {
                        await this.processEmergencyMode(remainingInstances);
                    }
                    break;
                }
            } else {
                consecutiveFailures = 0; // Reset counter on success
            }

            // Update statistics
            actuallyRemovedCount += batchResults.successful;
            processedCount += batchResults.total;
            this.stats.successfulSells += batchResults.successful;
            this.stats.failedSells += batchResults.failed;

            // Progress update after each batch
            const overallProgress = ((batchEnd) / validInstances.length * 100).toFixed(1);
            console.log(`🎯 Batch ${batchIndex + 1} Complete: ${batchResults.successful}/${batchResults.total} successful`);
            console.log(`📊 Overall Progress: ${overallProgress}% (${batchEnd}/${validInstances.length})`);
            console.log(`📊 Total Stats: ${this.stats.successfulSells} success, ${this.stats.failedSells} failed, ${this.stats.accessViolations} access violations`);

            // Check actual entity count reduction after each batch
            const currentEntityCount = this.getCurrentEntityCount();
            if (currentEntityCount >= 0) {
                const actualReduction = lastEntityCountCheck - currentEntityCount;
                console.log(`🎯 Entity Count Update: ${currentEntityCount} remaining (reduced by ${actualReduction} this batch)`);
                console.log(`📈 Batch Efficiency: ${batchResults.successful}/${batchResults.total} operations resulted in entity removal`);
                lastEntityCountCheck = currentEntityCount;
            }

            // Adaptive delay based on batch performance
            if (batchIndex < totalBatches - 1) {
                let delayMs = 2000; // Base delay

                // Increase delay if we're seeing access violations
                if (batchResults.failed > batchResults.successful) {
                    delayMs = 5000; // Longer delay for problematic batches
                    console.log(`⏳ Extended delay due to batch issues - waiting 5 seconds...`);
                } else {
                    console.log(`⏳ Waiting 2 seconds before next batch...`);
                }

                await new Promise(resolve => setTimeout(resolve, delayMs));
            }
        }

        // Final entity count check
        const finalEntityCount = this.getCurrentEntityCount();
        if (finalEntityCount >= 0) {
            const totalReduction = initialCount - finalEntityCount;
            console.log(`\n🎯 FINAL ENTITY COUNT ANALYSIS:`);
            console.log(`   Initial Entities: ${initialCount}`);
            console.log(`   Final Entities: ${finalEntityCount}`);
            console.log(`   Total Reduction: ${totalReduction}`);
            console.log(`   Removal Rate: ${((totalReduction / initialCount) * 100).toFixed(1)}%`);
            console.log(`   Operations vs Actual Removals: ${this.stats.successfulSells} operations → ${totalReduction} actual removals`);
        }
    }

    /**
     * Generate final statistics report
     */
    private generateReport(): void {
        this.stats.endTime = Date.now();
        this.stats.executionTimeMs = this.stats.endTime - this.stats.startTime;

        console.log("\n" + "=".repeat(60));
        console.log("🗑️ FRIDA RUIN SELLER - EXECUTION REPORT");
        console.log("=".repeat(60));
        console.log(`📊 Instance Discovery:`);
        console.log(`   Total Instances: ${this.stats.totalInstances}`);
        console.log(`   Valid GoodyHuts: ${this.stats.validGoodyHuts}`);
        console.log(`   Completed Instances: ${this.stats.completedInstances}`);
        console.log("");
        console.log(`🗑️ Ruin Selling Operations:`);
        console.log(`   Attempts: ${this.stats.ruinSellAttempts}`);
        console.log(`   Successful: ${this.stats.successfulSells}`);
        console.log(`   Failed: ${this.stats.failedSells}`);

        if (this.stats.ruinSellAttempts > 0) {
            const successRate = (this.stats.successfulSells / this.stats.ruinSellAttempts * 100).toFixed(1);
            console.log(`   Success Rate: ${successRate}%`);
        }

        console.log("");
        console.log(`⚠️ Error Analysis:`);
        console.log(`   Access Violations: ${this.stats.accessViolations}`);
        console.log(`   Method Not Found: ${this.stats.methodNotFound}`);
        console.log(`   Connection Errors: ${this.stats.connectionErrors}`);
        console.log(`   Retry Attempts: ${this.stats.retryAttempts}`);
        
        console.log("");
        console.log(`⏱️ Performance Metrics:`);
        console.log(`   Execution Time: ${this.stats.executionTimeMs}ms (${(this.stats.executionTimeMs! / 1000).toFixed(2)}s)`);

        if (this.stats.ruinSellAttempts > 0 && this.stats.executionTimeMs) {
            const opsPerSecond = (this.stats.ruinSellAttempts / (this.stats.executionTimeMs / 1000)).toFixed(2);
            console.log(`   Operations/Second: ${opsPerSecond}`);
        }

        console.log("=".repeat(60));

        if (this.stats.successfulSells > 0) {
            console.log(`✅ Successfully processed ${this.stats.successfulSells} ruin selling operations!`);
        } else {
            console.log(`ℹ️ No ruin selling operations were completed.`);
        }

        console.log("🗑️ Frida Ruin Seller execution completed.");
    }

    /**
     * Main execution method
     */
    public async run(): Promise<void> {
        try {
            console.log("🗑️ Frida Ruin Seller - Starting automated ruin selling...");

            // Initialize Il2Cpp domain
            if (!(await this.initialize())) {
                console.log("❌ Initialization failed - aborting execution");
                return;
            }

            // Process all instances
            await this.processAllInstances();

            // Generate final report
            this.generateReport();

        } catch (error) {
            console.log(`❌ Fatal error during execution: ${error}`);
            this.generateReport();
        }
    }
}

// Script execution entry point using frida-il2cpp-bridge
Il2Cpp.perform(() => {
    console.log("🚀 Frida Ruin Seller - Il2Cpp bridge context established");

    // Enhanced global error handler for critical errors and crashes
    process.on('uncaughtException', (error) => {
        const errorMsg = error.toString();
        console.log(`❌ Critical error detected: ${errorMsg}`);

        if (errorMsg.includes('SIGSEGV') || errorMsg.includes('access violation') || errorMsg.includes('segmentation')) {
            console.log("🚨 MEMORY ACCESS VIOLATION DETECTED - Process crash prevented");
            console.log("🔧 Implementing emergency recovery procedures...");

            // Log crash details for debugging
            console.log(`📊 Crash Context:`);
            console.log(`   Error Type: Memory Access Violation`);
            console.log(`   Error Details: ${errorMsg}`);
            console.log(`   Recovery Action: Graceful shutdown with error reporting`);

            // Prevent process crash by handling the error gracefully
            setTimeout(() => {
                console.log("🔄 Emergency recovery completed - script will continue with enhanced safety measures");
            }, 1000);
        } else {
            console.log("🔄 Attempting graceful recovery from non-critical error...");
        }
    });

    // Handle unhandled promise rejections that could cause crashes
    process.on('unhandledRejection', (reason, promise) => {
        console.log(`❌ Unhandled promise rejection: ${reason}`);
        console.log("🔧 Promise rejection handled to prevent crash");
    });

    // Handle SIGTERM and SIGINT for graceful shutdown
    process.on('SIGTERM', () => {
        console.log("🛑 SIGTERM received - performing graceful shutdown");
    });

    process.on('SIGINT', () => {
        console.log("🛑 SIGINT received - performing graceful shutdown");
    });

    // Wait for game to be fully loaded before starting
    setTimeout(async () => {
        let retryCount = 0;
        const maxRetries = 3;

        while (retryCount < maxRetries) {
            try {
                console.log(`🔧 Starting ruin seller execution (attempt ${retryCount + 1}/${maxRetries})...`);
                const ruinSeller = new FridaRuinSeller();
                await ruinSeller.run();
                console.log("✅ Execution completed successfully");
                break; // Success, exit retry loop

            } catch (error) {
                const errorMsg = String(error);
                console.log(`❌ Script execution failed: ${errorMsg}`);

                // Enhanced error classification and recovery
                if (errorMsg.includes("access violation") || errorMsg.includes("0x0") ||
                    errorMsg.includes("SIGSEGV") || errorMsg.includes("segmentation")) {
                    console.log("🚨 Memory access violation detected during execution");
                    console.log("🔧 This indicates unsafe memory access - implementing safety measures");

                    retryCount++;
                    if (retryCount < maxRetries) {
                        const delay = 2000 * retryCount; // Longer delay for memory issues
                        console.log(`⏳ Extended recovery delay: ${delay}ms before retry with enhanced safety...`);
                        await new Promise(resolve => setTimeout(resolve, delay));
                        console.log("🔧 Retry will use emergency mode processing only");
                    }

                } else if (errorMsg.includes("device") || errorMsg.includes("connection") ||
                    errorMsg.includes("lost") || errorMsg.includes("disconnected")) {
                    console.log("🔄 Connection error detected, attempting recovery...");
                    retryCount++;

                    if (retryCount < maxRetries) {
                        const delay = 1000 * retryCount; // Exponential backoff
                        console.log(`⏳ Waiting ${delay}ms before retry...`);
                        await new Promise(resolve => setTimeout(resolve, delay));
                    }

                } else if (errorMsg.includes("Il2Cpp") || errorMsg.includes("domain") ||
                          errorMsg.includes("assembly") || errorMsg.includes("class")) {
                    console.log("🔄 Il2Cpp initialization error detected, attempting recovery...");
                    retryCount++;

                    if (retryCount < maxRetries) {
                        const delay = 3000 * retryCount; // Longer delay for Il2Cpp issues
                        console.log(`⏳ Il2Cpp recovery delay: ${delay}ms before retry...`);
                        await new Promise(resolve => setTimeout(resolve, delay));
                    }

                } else {
                    // Non-recoverable error, exit
                    console.log("❌ Non-recoverable error detected, stopping execution");
                    console.log(`📊 Error Classification: Unknown/Critical`);
                    console.log(`📊 Error Details: ${errorMsg}`);
                    break;
                }
            }
        }

        if (retryCount >= maxRetries) {
            console.log("❌ Maximum retry attempts reached, execution failed");
        }
    }, 10000); // Wait 10 seconds for game to start loading before initializing
});

// Export for potential external usage
(globalThis as any).FridaRuinSeller = FridaRuinSeller;

console.log("🗑️ Frida Ruin Seller script loaded successfully - execution will begin shortly...");
